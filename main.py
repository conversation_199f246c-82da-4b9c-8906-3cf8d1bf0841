#!/usr/bin/env python3
"""
Bhagavad Gita Telegram Bot
A bot that sends daily verses from the Bhagavad Gita with practical applications
"""

import asyncio
import signal
import sys
import logging
from config import Config
from src.logger_setup import setup_logging, log_system_info, <PERSON>rror<PERSON><PERSON>ler
from src.bot import GitaBot
from src.scheduler import VerseScheduler

class GitaBotApplication:
    """Main application class for the Gita Bot"""
    
    def __init__(self):
        self.logger = None
        self.bot = None
        self.scheduler = None
        self.error_handler = None
        self.is_running = False
    
    def setup(self):
        """Setup the application"""
        try:
            # Setup logging first
            self.logger = setup_logging()
            log_system_info()
            
            # Validate configuration
            Config.validate_config()
            self.logger.info("Configuration validated successfully")
            
            # Initialize error handler
            self.error_handler = ErrorHandler()
            
            # Initialize bot
            self.bot = GitaBot()
            self.bot.setup_bot()
            self.logger.info("Bot initialized successfully")
            
            # Initialize scheduler
            self.scheduler = VerseScheduler(self.bot)
            self.logger.info("Scheduler initialized successfully")
            
            # Setup signal handlers for graceful shutdown
            signal.signal(signal.SIGINT, self._signal_handler)
            signal.signal(signal.SIGTERM, self._signal_handler)
            
            self.logger.info("Application setup completed successfully")
            return True
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Failed to setup application: {e}")
            else:
                print(f"Failed to setup application: {e}")
            return False
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        self.logger.info(f"Received signal {signum}, initiating graceful shutdown...")
        self.shutdown()
    
    def start(self):
        """Start the bot application"""
        if not self.setup():
            sys.exit(1)
        
        try:
            self.logger.info("Starting Gita Bot application...")
            self.is_running = True
            
            # Start the scheduler
            self.scheduler.start_scheduler()
            
            # Start the bot (this will block)
            self.logger.info("Bot is now running. Press Ctrl+C to stop.")
            self.bot.run()
            
        except KeyboardInterrupt:
            self.logger.info("Received keyboard interrupt")
        except Exception as e:
            self.logger.error(f"Unexpected error in main loop: {e}")
        finally:
            self.shutdown()
    
    def shutdown(self):
        """Graceful shutdown of the application"""
        if not self.is_running:
            return
        
        self.logger.info("Shutting down Gita Bot application...")
        self.is_running = False
        
        try:
            # Stop scheduler
            if self.scheduler:
                self.scheduler.stop_scheduler()
                self.logger.info("Scheduler stopped")
            
            # Stop bot
            if self.bot and self.bot.application:
                self.bot.application.stop()
                self.logger.info("Bot stopped")
            
            self.logger.info("Gita Bot application shutdown completed")
            
        except Exception as e:
            self.logger.error(f"Error during shutdown: {e}")
    
    async def test_functionality(self):
        """Test bot functionality (for development/debugging)"""
        try:
            self.logger.info("Testing bot functionality...")
            
            # Test database connection
            from src.database import DatabaseManager
            db = DatabaseManager()
            subscribed_groups = db.get_subscribed_groups()
            self.logger.info(f"Database test: Found {len(subscribed_groups)} subscribed groups")
            
            # Test OpenRouter API
            from src.openrouter_service import OpenRouterService
            openrouter = OpenRouterService()
            verse_data = openrouter.generate_verse_content()
            if verse_data:
                self.logger.info("OpenRouter API test: Successfully generated verse content")
            else:
                self.logger.error("OpenRouter API test: Failed to generate verse content")
            
            # Test scheduler
            if self.scheduler:
                status = self.scheduler.get_scheduler_status()
                self.logger.info(f"Scheduler test: {status}")
            
            self.logger.info("Functionality test completed")
            
        except Exception as e:
            self.logger.error(f"Error in functionality test: {e}")

def main():
    """Main entry point"""
    app = GitaBotApplication()
    
    # Check if we're running in test mode
    if len(sys.argv) > 1 and sys.argv[1] == '--test':
        if app.setup():
            asyncio.run(app.test_functionality())
        return
    
    # Normal startup
    app.start()

if __name__ == "__main__":
    main()
