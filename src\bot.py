import logging
from datetime import date
from telegram import Update, Bot
from telegram.ext import Application, CommandHandler, MessageHandler, filters, ContextTypes
from telegram.constants import ParseMode
from config import Config
from src.database import DatabaseManager
from src.openrouter_service import OpenRouterService

class GitaBot:
    """Main Telegram Bot class for Bhagavad Gita verses"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.db = DatabaseManager()
        self.openrouter = OpenRouterService()
        self.application = None
        
    def setup_bot(self) -> Application:
        """Setup the Telegram bot application"""
        self.application = Application.builder().token(Config.TELEGRAM_BOT_TOKEN).build()
        
        # Add command handlers
        self.application.add_handler(CommandHandler("start", self.start_command))
        self.application.add_handler(CommandHandler("help", self.help_command))
        self.application.add_handler(CommandHandler("verse", self.verse_command))
        self.application.add_handler(CommandHandler("subscribe", self.subscribe_command))
        self.application.add_handler(CommandHandler("unsubscribe", self.unsubscribe_command))
        self.application.add_handler(CommandHandler("status", self.status_command))
        
        # Add message handler for group messages
        self.application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, self.handle_message))
        
        self.logger.info("Bot setup completed")
        return self.application
    
    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /start command"""
        user = update.effective_user
        chat = update.effective_chat
        
        # Add user to database
        self.db.add_or_update_user(
            user_id=user.id,
            username=user.username,
            first_name=user.first_name,
            last_name=user.last_name
        )
        
        welcome_message = f"""
🕉️ **Welcome to the Bhagavad Gita Bot!** 🕉️

Hello {user.first_name}! I'm here to share the timeless wisdom of the Bhagavad Gita with you.

**Available Commands:**
• `/verse` - Get a random verse with meaning and daily applications
• `/subscribe` - Subscribe your group to daily verses (4:00 AM IST)
• `/unsubscribe` - Unsubscribe from daily verses
• `/status` - Check your usage status
• `/help` - Show this help message

**Usage Limits:**
• Personal chats: 1 verse per day
• Groups: 5 verses per day

🙏 _May Lord Krishna's wisdom guide your path!_ 🙏
"""
        
        await update.message.reply_text(welcome_message, parse_mode=ParseMode.MARKDOWN)
    
    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /help command"""
        help_message = """
🕉️ **Bhagavad Gita Bot Help** 🕉️

**Commands:**
• `/start` - Start the bot and see welcome message
• `/verse` - Get a random verse with Sanskrit, transliteration, English translation, and daily life applications
• `/subscribe` - Subscribe your group to receive daily verses at 4:00 AM IST
• `/unsubscribe` - Unsubscribe your group from daily verses
• `/status` - Check your daily usage status
• `/help` - Show this help message

**Features:**
• Daily automatic verses sent to subscribed groups at 4:00 AM IST
• Practical applications for students, professionals, and general life
• Usage limits to encourage mindful learning

**Usage Limits:**
• Personal chats: 1 verse per day
• Groups: 5 verses per day

**Note:** After reaching your daily limit, please take time to reflect on and understand the verse before requesting another.

🙏 _Hari Om!_ 🙏
"""
        
        await update.message.reply_text(help_message, parse_mode=ParseMode.MARKDOWN)
    
    async def verse_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /verse command"""
        user = update.effective_user
        chat = update.effective_chat
        
        # Add user to database if not exists
        self.db.add_or_update_user(
            user_id=user.id,
            username=user.username,
            first_name=user.first_name,
            last_name=user.last_name
        )
        
        # Check usage limits
        if chat.type == 'private':
            # Private chat - check user limit
            usage_count = self.db.get_user_daily_usage(user.id)
            if usage_count >= Config.MAX_USER_VERSES_PER_DAY:
                limit_message = "You've reached your daily limit. Please first learn and understand today's verse before requesting another. 🙏"
                await update.message.reply_text(limit_message)
                return
        else:
            # Group chat - check group limit
            self.db.add_or_update_group(
                group_id=chat.id,
                group_title=chat.title,
                group_type=chat.type
            )
            
            usage_count = self.db.get_group_daily_usage(chat.id)
            if usage_count >= Config.MAX_GROUP_VERSES_PER_DAY:
                limit_message = "Due to limitation I can't generate more verses today"
                await update.message.reply_text(limit_message)
                return
        
        # Generate and send verse
        await self.send_verse(update, context, is_scheduled=False)
    
    async def subscribe_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /subscribe command"""
        chat = update.effective_chat
        user = update.effective_user
        
        if chat.type == 'private':
            await update.message.reply_text(
                "Subscription is only available for groups. Add me to a group and use /subscribe there."
            )
            return
        
        # Check if user is admin (in a real implementation, you'd check admin status)
        # For now, we'll allow any user to subscribe/unsubscribe
        
        # Add/update group and subscribe
        success = self.db.subscribe_group(chat.id)
        self.db.add_or_update_group(
            group_id=chat.id,
            group_title=chat.title,
            group_type=chat.type,
            is_subscribed=True
        )
        
        if success:
            message = f"""
✅ **Subscription Successful!**

This group is now subscribed to daily Bhagavad Gita verses.

📅 **Schedule:** Daily at 4:00 AM IST
🎯 **Limit:** 5 verses per day for the group
📚 **Content:** Sanskrit verse, transliteration, English translation, and practical applications

🙏 _May divine wisdom bless this group!_ 🙏
"""
        else:
            message = "❌ Failed to subscribe. Please try again later."
        
        await update.message.reply_text(message, parse_mode=ParseMode.MARKDOWN)

    async def unsubscribe_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /unsubscribe command"""
        chat = update.effective_chat

        if chat.type == 'private':
            await update.message.reply_text(
                "Unsubscription is only available for groups."
            )
            return

        success = self.db.unsubscribe_group(chat.id)

        if success:
            message = """
✅ **Unsubscription Successful!**

This group has been unsubscribed from daily Bhagavad Gita verses.

You can still use the `/verse` command to get verses manually.

🙏 _Thank you for your time with us!_ 🙏
"""
        else:
            message = "❌ Failed to unsubscribe. Please try again later."

        await update.message.reply_text(message, parse_mode=ParseMode.MARKDOWN)

    async def status_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /status command"""
        user = update.effective_user
        chat = update.effective_chat

        if chat.type == 'private':
            usage_count = self.db.get_user_daily_usage(user.id)
            remaining = Config.MAX_USER_VERSES_PER_DAY - usage_count

            message = f"""
📊 **Your Daily Status**

🗓️ **Date:** {date.today().strftime('%B %d, %Y')}
📖 **Verses Used:** {usage_count}/{Config.MAX_USER_VERSES_PER_DAY}
⏳ **Remaining:** {max(0, remaining)}

🕐 **Reset Time:** Daily at 12:00 AM IST

🙏 _Use your verses wisely for spiritual growth!_ 🙏
"""
        else:
            usage_count = self.db.get_group_daily_usage(chat.id)
            remaining = Config.MAX_GROUP_VERSES_PER_DAY - usage_count

            # Check subscription status
            subscribed_groups = self.db.get_subscribed_groups()
            is_subscribed = chat.id in subscribed_groups

            message = f"""
📊 **Group Daily Status**

🗓️ **Date:** {date.today().strftime('%B %d, %Y')}
📖 **Verses Used:** {usage_count}/{Config.MAX_GROUP_VERSES_PER_DAY}
⏳ **Remaining:** {max(0, remaining)}
📬 **Subscription:** {'✅ Active' if is_subscribed else '❌ Not Subscribed'}

🕐 **Reset Time:** Daily at 12:00 AM IST
📅 **Daily Verse Time:** 4:00 AM IST (if subscribed)

🙏 _May wisdom flourish in this group!_ 🙏
"""

        await update.message.reply_text(message, parse_mode=ParseMode.MARKDOWN)

    async def handle_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle regular messages (for future features)"""
        # For now, we don't respond to regular messages
        # This can be extended for natural language processing
        pass

    async def send_verse(self, update: Update, context: ContextTypes.DEFAULT_TYPE, is_scheduled: bool = False):
        """Send a verse to the chat"""
        chat = update.effective_chat
        user = update.effective_user

        try:
            # Show typing indicator
            await context.bot.send_chat_action(chat_id=chat.id, action="typing")

            # Generate verse content
            verse_data = self.openrouter.generate_verse_content()

            if not verse_data:
                error_message = "🙏 Sorry, I couldn't generate a verse at this time. Please try again later."
                await update.message.reply_text(error_message)
                return

            # Format the message
            formatted_message = self.openrouter.format_verse_message(verse_data)

            # Send the verse
            await update.message.reply_text(formatted_message, parse_mode=ParseMode.MARKDOWN)

            # Update usage counts
            if chat.type == 'private':
                self.db.increment_user_usage(user.id)
            else:
                self.db.increment_group_usage(chat.id)

            # Add to verse history
            self.db.add_verse_history(
                chat_id=chat.id,
                chat_type=chat.type,
                verse_content=formatted_message,
                is_scheduled=is_scheduled
            )

            self.logger.info(f"Verse sent to chat {chat.id} ({'scheduled' if is_scheduled else 'manual'})")

        except Exception as e:
            self.logger.error(f"Error sending verse to chat {chat.id}: {e}")
            error_message = "🙏 Sorry, there was an error generating the verse. Please try again later."
            await update.message.reply_text(error_message)

    async def send_scheduled_verse_to_group(self, group_id: int):
        """Send scheduled verse to a specific group"""
        try:
            # Check if group has reached daily limit
            usage_count = self.db.get_group_daily_usage(group_id)
            if usage_count >= Config.MAX_GROUP_VERSES_PER_DAY:
                self.logger.info(f"Group {group_id} has reached daily limit, skipping scheduled verse")
                return

            # Generate verse content
            verse_data = self.openrouter.generate_verse_content()

            if not verse_data:
                self.logger.error(f"Failed to generate verse content for scheduled send to group {group_id}")
                return

            # Format the message with a special header for scheduled verses
            formatted_message = f"🌅 **Daily Verse - {date.today().strftime('%B %d, %Y')}** 🌅\n\n"
            formatted_message += self.openrouter.format_verse_message(verse_data)

            # Send the verse
            await self.application.bot.send_message(
                chat_id=group_id,
                text=formatted_message,
                parse_mode=ParseMode.MARKDOWN
            )

            # Update usage count
            self.db.increment_group_usage(group_id)

            # Add to verse history
            self.db.add_verse_history(
                chat_id=group_id,
                chat_type='group',
                verse_content=formatted_message,
                is_scheduled=True
            )

            self.logger.info(f"Scheduled verse sent to group {group_id}")

        except Exception as e:
            self.logger.error(f"Error sending scheduled verse to group {group_id}: {e}")

    def run(self):
        """Run the bot"""
        self.logger.info("Starting Gita Bot...")
        self.application.run_polling()
