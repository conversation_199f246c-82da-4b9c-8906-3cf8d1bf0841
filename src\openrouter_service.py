import openai
import logging
import random
from typing import Optional, Dict, Any
from config import Config

class OpenRouterService:
    """Service for interacting with OpenRouter API using DeepSeek model"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.client = openai.OpenAI(
            base_url=Config.OPENROUTER_BASE_URL,
            api_key=Config.OPENROUTER_API_KEY
        )
        self.model = Config.OPENROUTER_MODEL
        
        # Bhagavad Gita has 18 chapters with varying verse counts
        self.chapter_verse_counts = {
            1: 47, 2: 72, 3: 43, 4: 42, 5: 29, 6: 47, 7: 30, 8: 28,
            9: 34, 10: 42, 11: 55, 12: 20, 13: 35, 14: 27, 15: 20,
            16: 24, 17: 28, 18: 78
        }
    
    def get_random_verse_reference(self) -> tuple[int, int]:
        """Get a random chapter and verse number"""
        chapter = random.randint(1, 18)
        verse = random.randint(1, self.chapter_verse_counts[chapter])
        return chapter, verse
    
    def generate_verse_content(self, chapter: int = None, verse: int = None) -> Optional[Dict[str, Any]]:
        """
        Generate comprehensive verse content including Sanskrit, transliteration, 
        English translation, and daily life applications
        """
        try:
            # If no specific verse is requested, get a random one
            if chapter is None or verse is None:
                chapter, verse = self.get_random_verse_reference()
            
            # Validate chapter and verse numbers
            if not self._is_valid_verse(chapter, verse):
                self.logger.error(f"Invalid verse reference: Chapter {chapter}, Verse {verse}")
                return None
            
            prompt = self._create_verse_prompt(chapter, verse)
            
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": "You are a knowledgeable scholar of the Bhagavad Gita. Provide accurate, authentic, and meaningful interpretations of the verses with practical applications for modern life."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.7,
                max_tokens=1500
            )
            
            content = response.choices[0].message.content
            parsed_content = self._parse_response(content, chapter, verse)
            
            if parsed_content:
                self.logger.info(f"Successfully generated content for Chapter {chapter}, Verse {verse}")
                return parsed_content
            else:
                self.logger.error(f"Failed to parse response for Chapter {chapter}, Verse {verse}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error generating verse content: {e}")
            return None
    
    def _is_valid_verse(self, chapter: int, verse: int) -> bool:
        """Validate if the chapter and verse numbers are valid"""
        if chapter < 1 or chapter > 18:
            return False
        if verse < 1 or verse > self.chapter_verse_counts.get(chapter, 0):
            return False
        return True
    
    def _create_verse_prompt(self, chapter: int, verse: int) -> str:
        """Create a detailed prompt for verse generation"""
        return f"""
Please provide comprehensive information for Bhagavad Gita Chapter {chapter}, Verse {verse} in the following format:

**SANSKRIT:**
[Provide the original Sanskrit verse in Devanagari script]

**TRANSLITERATION:**
[Provide the Sanskrit verse in Roman script with proper pronunciation guide]

**ENGLISH TRANSLATION:**
[Provide a clear, accurate English translation of the verse]

**DAILY LIFE APPLICATIONS:**

For Students:
[Provide 2-3 practical applications of this verse's wisdom for students in their academic and personal life]

For Working Professionals:
[Provide 2-3 practical applications of this verse's wisdom for working professionals in their career and workplace]

For General Life:
[Provide 2-3 practical applications of this verse's wisdom for general life situations and personal growth]

Please ensure the content is authentic, meaningful, and provides practical wisdom that can be applied in modern daily life.
"""

    def _parse_response(self, content: str, chapter: int, verse: int) -> Optional[Dict[str, Any]]:
        """Parse the AI response into structured format"""
        try:
            # Initialize the result dictionary
            result = {
                'chapter': chapter,
                'verse': verse,
                'sanskrit': '',
                'transliteration': '',
                'english_translation': '',
                'applications': {
                    'students': '',
                    'professionals': '',
                    'general': ''
                }
            }

            # Split content into sections
            sections = content.split('**')
            current_section = None
            current_content = []

            for section in sections:
                section = section.strip()
                if not section:
                    continue

                # Check for section headers
                if 'SANSKRIT:' in section.upper():
                    current_section = 'sanskrit'
                    current_content = []
                elif 'TRANSLITERATION:' in section.upper():
                    if current_section == 'sanskrit':
                        result['sanskrit'] = '\n'.join(current_content).strip()
                    current_section = 'transliteration'
                    current_content = []
                elif 'ENGLISH TRANSLATION:' in section.upper():
                    if current_section == 'transliteration':
                        result['transliteration'] = '\n'.join(current_content).strip()
                    current_section = 'english_translation'
                    current_content = []
                elif 'DAILY LIFE APPLICATIONS:' in section.upper():
                    if current_section == 'english_translation':
                        result['english_translation'] = '\n'.join(current_content).strip()
                    current_section = 'applications'
                    current_content = []
                else:
                    # This is content for the current section
                    if current_section:
                        current_content.append(section)

            # Handle the last section
            if current_section == 'applications' and current_content:
                apps_text = '\n'.join(current_content)
                result['applications'] = self._parse_applications(apps_text)
            elif current_section and current_content:
                result[current_section] = '\n'.join(current_content).strip()

            # Validate that we have the essential content
            if result['sanskrit'] and result['english_translation']:
                return result
            else:
                self.logger.warning(f"Incomplete content parsed for Chapter {chapter}, Verse {verse}")
                return None

        except Exception as e:
            self.logger.error(f"Error parsing response: {e}")
            return None

    def _parse_applications(self, apps_text: str) -> Dict[str, str]:
        """Parse the applications section into categories"""
        applications = {
            'students': '',
            'professionals': '',
            'general': ''
        }

        try:
            lines = apps_text.split('\n')
            current_category = None
            current_content = []

            for line in lines:
                line = line.strip()
                if not line:
                    continue

                if 'For Students:' in line:
                    current_category = 'students'
                    current_content = []
                elif 'For Working Professionals:' in line:
                    if current_category == 'students':
                        applications['students'] = '\n'.join(current_content).strip()
                    current_category = 'professionals'
                    current_content = []
                elif 'For General Life:' in line:
                    if current_category == 'professionals':
                        applications['professionals'] = '\n'.join(current_content).strip()
                    current_category = 'general'
                    current_content = []
                else:
                    if current_category:
                        current_content.append(line)

            # Handle the last category
            if current_category == 'general' and current_content:
                applications['general'] = '\n'.join(current_content).strip()

        except Exception as e:
            self.logger.error(f"Error parsing applications: {e}")

        return applications

    def format_verse_message(self, verse_data: Dict[str, Any]) -> str:
        """Format the verse data into a beautiful Telegram message"""
        if not verse_data:
            return "Sorry, I couldn't generate the verse content at this time."

        message = f"🕉️ **Bhagavad Gita - Chapter {verse_data['chapter']}, Verse {verse_data['verse']}** 🕉️\n\n"

        if verse_data.get('sanskrit'):
            message += f"**संस्कृत (Sanskrit):**\n{verse_data['sanskrit']}\n\n"

        if verse_data.get('transliteration'):
            message += f"**Transliteration:**\n{verse_data['transliteration']}\n\n"

        if verse_data.get('english_translation'):
            message += f"**English Translation:**\n{verse_data['english_translation']}\n\n"

        # Add applications
        applications = verse_data.get('applications', {})
        if any(applications.values()):
            message += "**📚 Daily Life Applications:**\n\n"

            if applications.get('students'):
                message += f"**For Students:**\n{applications['students']}\n\n"

            if applications.get('professionals'):
                message += f"**For Working Professionals:**\n{applications['professionals']}\n\n"

            if applications.get('general'):
                message += f"**For General Life:**\n{applications['general']}\n\n"

        message += "🙏 _May this wisdom guide you on your spiritual journey_ 🙏"

        return message
