# 🕉️ Bhagavad Gita Telegram Bot - Project Overview

## 📁 Complete Project Structure

```
gita-bot/
├── 📄 main.py                    # Main application entry point
├── ⚙️ config.py                  # Configuration management
├── 📋 requirements.txt           # Python dependencies
├── 📝 .env.example              # Environment variables template
├── 🛠️ install.py                # Automated installation script
├── 🧪 test_bot.py               # Comprehensive test suite
├── 📖 README.md                 # Main documentation
├── 📚 SETUP_GUIDE.md            # Detailed setup instructions
├── 📊 PROJECT_OVERVIEW.md       # This file
├── 📁 src/                      # Source code directory
│   ├── 🤖 bot.py                # Main bot class and handlers
│   ├── 🗄️ database.py           # Database management (SQLite)
│   ├── 🤖 openrouter_service.py # AI service integration (DeepSeek)
│   ├── ⏰ scheduler.py           # Daily scheduling system
│   ├── 📝 logger_setup.py       # Logging configuration
│   └── 📄 __init__.py           # Package initialization
└── 📁 logs/                     # Log files directory (auto-created)
```

## ✨ Key Features Implemented

### 🎯 Core Functionality
- ✅ **Scheduled Daily Messages**: Automatic verse sending at 4:00 AM IST
- ✅ **Manual Verse Generation**: `/verse` command for on-demand verses
- ✅ **Smart Usage Limits**: 5 verses/day for groups, 1 verse/day for users
- ✅ **Group Management**: Subscribe/unsubscribe functionality

### 📱 Telegram Integration
- ✅ **Command Handlers**: `/start`, `/help`, `/verse`, `/subscribe`, `/unsubscribe`, `/status`
- ✅ **Message Formatting**: Beautiful Markdown formatting with emojis
- ✅ **Error Handling**: Graceful error responses and logging
- ✅ **Group Support**: Full group chat functionality

### 🤖 AI Integration
- ✅ **OpenRouter API**: Integration with DeepSeek model
- ✅ **Verse Generation**: Sanskrit, transliteration, English translation
- ✅ **Daily Applications**: Practical guidance for students, professionals, general life
- ✅ **Content Parsing**: Structured response parsing and formatting

### 🗄️ Data Management
- ✅ **SQLite Database**: Persistent storage for all data
- ✅ **User Tracking**: User information and daily usage limits
- ✅ **Group Management**: Subscription status and usage tracking
- ✅ **Verse History**: Complete history of sent verses
- ✅ **Daily Reset**: Automatic usage counter reset

### ⏰ Scheduling System
- ✅ **Daily Automation**: 4:00 AM IST scheduled sending
- ✅ **Timezone Support**: Configurable timezone (IST default)
- ✅ **Background Processing**: Non-blocking scheduler thread
- ✅ **Error Recovery**: Robust error handling for scheduled tasks

### 📝 Logging & Monitoring
- ✅ **Comprehensive Logging**: Multi-level logging system
- ✅ **Log Rotation**: Automatic log file rotation
- ✅ **Error Tracking**: Separate error log files
- ✅ **Performance Monitoring**: Detailed operation logging

## 🔧 Technical Architecture

### 🏗️ Design Patterns
- **Modular Architecture**: Separated concerns across multiple modules
- **Configuration Management**: Environment-based configuration
- **Error Handling**: Centralized error handling and logging
- **Async Programming**: Proper async/await usage for Telegram API

### 🛡️ Security Features
- **Environment Variables**: Secure API key management
- **Input Validation**: Proper validation of user inputs
- **Rate Limiting**: Built-in usage limits to prevent abuse
- **Error Sanitization**: Safe error messages without sensitive data

### 📊 Database Schema
```sql
-- Users table
users (user_id, username, first_name, last_name, created_at, is_active)

-- Groups table  
groups (group_id, group_title, group_type, is_subscribed, created_at, updated_at)

-- Daily usage tracking
user_daily_usage (id, user_id, usage_date, verse_count, created_at)
group_daily_usage (id, group_id, usage_date, verse_count, created_at)

-- Verse history
verse_history (id, chat_id, chat_type, verse_content, sent_at, is_scheduled)
```

## 🚀 Quick Start Commands

```bash
# 1. Install dependencies
python install.py

# 2. Configure environment
# Edit .env file with your API keys

# 3. Test setup
python test_bot.py

# 4. Run the bot
python main.py

# 5. Test functionality
python main.py --test
```

## 📋 Configuration Options

| Setting | Description | Default |
|---------|-------------|---------|
| `DAILY_SEND_TIME` | Daily verse time | `04:00` |
| `TIMEZONE` | Bot timezone | `Asia/Kolkata` |
| `MAX_GROUP_VERSES_PER_DAY` | Group daily limit | `5` |
| `MAX_USER_VERSES_PER_DAY` | User daily limit | `1` |
| `LOG_LEVEL` | Logging verbosity | `INFO` |

## 🎯 Usage Scenarios

### 👥 For Groups
1. Add bot to group
2. Use `/subscribe` to enable daily verses
3. Receive automatic verses at 4:00 AM IST
4. Use `/verse` for additional verses (up to 5/day)
5. Check `/status` for usage information

### 👤 For Individual Users
1. Start private chat with bot
2. Use `/verse` to get verses (1/day limit)
3. Receive personalized guidance and applications
4. Check `/status` for daily usage

## 🔍 Monitoring & Maintenance

### 📊 Log Files
- `gita_bot.log` - General application logs
- `gita_bot_errors.log` - Error-specific logs
- Automatic rotation when files exceed 10MB

### 🗄️ Database Maintenance
- SQLite database with automatic schema creation
- Daily usage counters reset automatically at midnight
- Verse history maintained for analytics

### 🔧 Health Checks
- Built-in test suite (`test_bot.py`)
- Configuration validation
- API connectivity testing
- Database integrity checks

## 🎉 Success Metrics

The bot successfully implements all requested features:

✅ **Scheduled Daily Messages** - 4:00 AM IST automatic sending  
✅ **Manual Verse Generation** - `/verse` command functionality  
✅ **Complete Message Format** - Sanskrit, transliteration, English, applications  
✅ **Usage Limitations** - 5 verses/group, 1 verse/user per day  
✅ **Group Management** - Subscribe/unsubscribe functionality  
✅ **Error Handling** - Comprehensive error management  
✅ **Persistent Storage** - SQLite database for all tracking  
✅ **Time Zone Support** - IST scheduling with configurable timezone  

## 🙏 Ready to Spread Wisdom!

Your Bhagavad Gita Telegram Bot is now complete and ready to share the timeless wisdom of the Gita with users around the world. The bot combines modern technology with ancient wisdom to provide practical spiritual guidance for daily life.

**May this bot serve as a bridge between eternal wisdom and contemporary living! 🕉️**
