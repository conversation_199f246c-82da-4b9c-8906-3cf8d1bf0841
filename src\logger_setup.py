import logging
import logging.handlers
import os
from datetime import datetime
from config import Config

def setup_logging():
    """Setup comprehensive logging for the Gita Bot"""
    
    # Create logs directory if it doesn't exist
    log_dir = os.path.dirname(Config.LOG_FILE) or 'logs'
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # Configure root logger
    logger = logging.getLogger()
    logger.setLevel(getattr(logging, Config.LOG_LEVEL.upper(), logging.INFO))
    
    # Clear any existing handlers
    logger.handlers.clear()
    
    # Create formatters
    detailed_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    simple_formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # File handler with rotation
    file_handler = logging.handlers.RotatingFileHandler(
        Config.LOG_FILE,
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(detailed_formatter)
    logger.addHandler(file_handler)
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(simple_formatter)
    logger.addHandler(console_handler)
    
    # Error file handler for critical errors
    error_handler = logging.handlers.RotatingFileHandler(
        Config.LOG_FILE.replace('.log', '_errors.log'),
        maxBytes=5*1024*1024,  # 5MB
        backupCount=3,
        encoding='utf-8'
    )
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(detailed_formatter)
    logger.addHandler(error_handler)
    
    # Suppress some noisy loggers
    logging.getLogger('httpx').setLevel(logging.WARNING)
    logging.getLogger('telegram').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    
    logger.info("Logging setup completed")
    logger.info(f"Log level: {Config.LOG_LEVEL}")
    logger.info(f"Log file: {Config.LOG_FILE}")
    
    return logger

def log_system_info():
    """Log system and configuration information"""
    logger = logging.getLogger(__name__)
    
    logger.info("=== Gita Bot Starting ===")
    logger.info(f"Timestamp: {datetime.now()}")
    logger.info(f"Timezone: {Config.TIMEZONE}")
    logger.info(f"Daily send time: {Config.DAILY_SEND_TIME}")
    logger.info(f"Max user verses per day: {Config.MAX_USER_VERSES_PER_DAY}")
    logger.info(f"Max group verses per day: {Config.MAX_GROUP_VERSES_PER_DAY}")
    logger.info(f"Database path: {Config.DATABASE_PATH}")
    logger.info(f"OpenRouter model: {Config.OPENROUTER_MODEL}")
    logger.info("=== Configuration Loaded ===")

class ErrorHandler:
    """Centralized error handling for the bot"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def handle_api_error(self, error: Exception, context: str = "API call"):
        """Handle API-related errors"""
        self.logger.error(f"API Error in {context}: {str(error)}")
        return f"🙏 Sorry, there was a technical issue with {context}. Please try again later."
    
    def handle_database_error(self, error: Exception, context: str = "database operation"):
        """Handle database-related errors"""
        self.logger.error(f"Database Error in {context}: {str(error)}")
        return f"🙏 Sorry, there was an issue with data storage. Please try again later."
    
    def handle_telegram_error(self, error: Exception, context: str = "Telegram operation"):
        """Handle Telegram API errors"""
        self.logger.error(f"Telegram Error in {context}: {str(error)}")
        return f"🙏 Sorry, there was an issue sending the message. Please try again later."
    
    def handle_general_error(self, error: Exception, context: str = "operation"):
        """Handle general errors"""
        self.logger.error(f"General Error in {context}: {str(error)}")
        return f"🙏 Sorry, there was an unexpected error. Please try again later."
    
    def log_user_action(self, user_id: int, action: str, success: bool = True, details: str = ""):
        """Log user actions for monitoring"""
        status = "SUCCESS" if success else "FAILED"
        message = f"User {user_id} - {action} - {status}"
        if details:
            message += f" - {details}"
        
        if success:
            self.logger.info(message)
        else:
            self.logger.warning(message)
    
    def log_group_action(self, group_id: int, action: str, success: bool = True, details: str = ""):
        """Log group actions for monitoring"""
        status = "SUCCESS" if success else "FAILED"
        message = f"Group {group_id} - {action} - {status}"
        if details:
            message += f" - {details}"
        
        if success:
            self.logger.info(message)
        else:
            self.logger.warning(message)
