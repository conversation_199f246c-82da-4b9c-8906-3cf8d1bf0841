# 🕉️ Bhagavad Gita Telegram Bot

A comprehensive Telegram bot that automatically sends daily Bhagavad Gita verses with practical applications for modern life. The bot provides Sanskrit verses, transliterations, English translations, and specific guidance for students, working professionals, and general life situations.

## ✨ Features

### 📅 Scheduled Daily Messages
- Automatically sends one verse daily at 4:00 AM IST to subscribed Telegram groups
- Beautiful formatting with Sanskrit, transliteration, and English translation
- Practical applications for different life situations

### 💬 Manual Verse Generation
- Users can request verses using the `/verse` command in private chats
- Instant verse generation with comprehensive content

### 🎯 Smart Usage Limits
- **Groups**: Maximum 5 verses per day per group
- **Individual Users**: 1 verse per day in private chats
- Automatic reset at midnight IST

### 🔧 Group Management
- Easy subscription/unsubscription for groups
- Admin-friendly commands
- Usage tracking and status monitoring

## 🚀 Quick Start

### Prerequisites
- Python 3.8 or higher
- Telegram Bot <PERSON>ken (from @BotFather)
- OpenRouter API Key (for DeepSeek access)

### Installation

1. **Clone the repository:**
```bash
git clone <repository-url>
cd gita-bot
```

2. **Install dependencies:**
```bash
pip install -r requirements.txt
```

3. **Setup environment variables:**
```bash
cp .env.example .env
# Edit .env with your actual tokens
```

4. **Configure your .env file:**
```env
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
OPENROUTER_API_KEY=your_openrouter_api_key_here
```

5. **Run the bot:**
```bash
python main.py
```

## 📋 Bot Commands

### For All Users
- `/start` - Welcome message and bot introduction
- `/help` - Show available commands and features
- `/verse` - Get a random verse (subject to daily limits)
- `/status` - Check daily usage status

### For Groups
- `/subscribe` - Subscribe group to daily verses at 4:00 AM IST
- `/unsubscribe` - Unsubscribe group from daily verses

## 🏗️ Project Structure

```
gita-bot/
├── main.py                 # Main application entry point
├── config.py              # Configuration management
├── requirements.txt       # Python dependencies
├── .env.example          # Environment variables template
├── src/
│   ├── bot.py            # Main bot class and handlers
│   ├── database.py       # Database management
│   ├── openrouter_service.py  # AI service integration
│   ├── scheduler.py      # Daily scheduling system
│   └── logger_setup.py   # Logging configuration
└── logs/                 # Log files directory
```

## ⚙️ Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `TELEGRAM_BOT_TOKEN` | Your Telegram bot token | Required |
| `OPENROUTER_API_KEY` | Your OpenRouter API key | Required |
| `OPENROUTER_MODEL` | AI model to use | `deepseek/deepseek-chat-v3.1:free` |
| `DATABASE_PATH` | SQLite database file path | `gita_bot.db` |
| `DAILY_SEND_TIME` | Daily verse send time | `04:00` |
| `TIMEZONE` | Timezone for scheduling | `Asia/Kolkata` |
| `MAX_GROUP_VERSES_PER_DAY` | Group daily limit | `5` |
| `MAX_USER_VERSES_PER_DAY` | User daily limit | `1` |
| `LOG_LEVEL` | Logging level | `INFO` |
| `LOG_FILE` | Log file path | `gita_bot.log` |

## 🗄️ Database Schema

The bot uses SQLite with the following tables:

- **users**: User information and tracking
- **groups**: Group information and subscription status
- **user_daily_usage**: Daily usage tracking for users
- **group_daily_usage**: Daily usage tracking for groups
- **verse_history**: History of sent verses

## 📊 Usage Limits & Responses

### Group Limits
- **Limit**: 5 verses per day
- **Exceeded Response**: "Due to limitation I can't generate more verses today"

### User Limits
- **Limit**: 1 verse per day in private chats
- **Exceeded Response**: "You've reached your daily limit. Please first learn and understand today's verse before requesting another"

## 🔧 Development

### Testing
```bash
# Test bot functionality
python main.py --test
```

### Logging
- Logs are stored in the `logs/` directory
- Automatic log rotation (10MB files, 5 backups)
- Separate error log for critical issues

## 🛠️ Troubleshooting

### Common Issues

1. **Bot not responding**
   - Check if the bot token is correct
   - Verify the bot is added to the group
   - Check logs for error messages

2. **Verses not generating**
   - Verify OpenRouter API key is valid
   - Check internet connectivity
   - Review API usage limits

3. **Scheduled verses not sending**
   - Ensure the bot is running continuously
   - Check timezone configuration
   - Verify groups are subscribed

### Log Files
- `gita_bot.log` - General application logs
- `gita_bot_errors.log` - Error-specific logs

## 📝 API Integration

The bot uses:
- **Telegram Bot API** for messaging
- **OpenRouter API** with DeepSeek model for verse generation
- **SQLite** for data persistence

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Bhagavad Gita translations and interpretations
- OpenRouter for AI API access
- Telegram Bot API
- Python community for excellent libraries

---

**May the wisdom of the Bhagavad Gita guide and inspire all users! 🕉️**
