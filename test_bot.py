#!/usr/bin/env python3
"""
Test script for Bhagavad Gita Telegram Bot
"""

import asyncio
import sys
import os

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_imports():
    """Test if all required modules can be imported"""
    print("🧪 Testing imports...")
    
    try:
        import telegram
        print("✅ python-telegram-bot imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import telegram: {e}")
        return False
    
    try:
        import openai
        print("✅ openai imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import openai: {e}")
        return False
    
    try:
        import schedule
        print("✅ schedule imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import schedule: {e}")
        return False
    
    try:
        from dotenv import load_dotenv
        print("✅ python-dotenv imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import dotenv: {e}")
        return False
    
    try:
        import pytz
        print("✅ pytz imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import pytz: {e}")
        return False
    
    return True

def test_config():
    """Test configuration loading"""
    print("\n🔧 Testing configuration...")
    
    try:
        from config import Config
        print("✅ Config module imported successfully")
        
        # Check if .env file exists
        if not os.path.exists('.env'):
            print("⚠️  .env file not found - using defaults")
        else:
            print("✅ .env file found")
        
        # Test required config
        if not Config.TELEGRAM_BOT_TOKEN:
            print("⚠️  TELEGRAM_BOT_TOKEN not set")
        else:
            print("✅ TELEGRAM_BOT_TOKEN configured")
        
        if not Config.OPENROUTER_API_KEY:
            print("⚠️  OPENROUTER_API_KEY not set")
        else:
            print("✅ OPENROUTER_API_KEY configured")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to load config: {e}")
        return False

def test_database():
    """Test database functionality"""
    print("\n🗄️ Testing database...")
    
    try:
        from database import DatabaseManager
        db = DatabaseManager()
        print("✅ Database initialized successfully")
        
        # Test basic operations
        test_user_id = 12345
        success = db.add_or_update_user(test_user_id, "test_user", "Test", "User")
        if success:
            print("✅ Database write test successful")
        else:
            print("❌ Database write test failed")
            return False
        
        usage = db.get_user_daily_usage(test_user_id)
        print(f"✅ Database read test successful (usage: {usage})")
        
        return True
        
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False

async def test_openrouter():
    """Test OpenRouter API (if configured)"""
    print("\n🤖 Testing OpenRouter API...")
    
    try:
        from config import Config
        if not Config.OPENROUTER_API_KEY:
            print("⚠️  OpenRouter API key not configured - skipping test")
            return True
        
        from openrouter_service import OpenRouterService
        service = OpenRouterService()
        print("✅ OpenRouter service initialized")
        
        # Test verse generation (this will make an actual API call)
        print("🔄 Testing verse generation (this may take a moment)...")
        verse_data = service.generate_verse_content()
        
        if verse_data:
            print("✅ Verse generation successful")
            print(f"   Chapter: {verse_data.get('chapter')}, Verse: {verse_data.get('verse')}")
            return True
        else:
            print("❌ Verse generation failed")
            return False
            
    except Exception as e:
        print(f"❌ OpenRouter test failed: {e}")
        return False

def test_bot_setup():
    """Test bot setup (without running)"""
    print("\n🤖 Testing bot setup...")
    
    try:
        from config import Config
        if not Config.TELEGRAM_BOT_TOKEN:
            print("⚠️  Telegram bot token not configured - skipping bot test")
            return True
        
        from bot import GitaBot
        bot = GitaBot()
        app = bot.setup_bot()
        
        if app:
            print("✅ Bot setup successful")
            return True
        else:
            print("❌ Bot setup failed")
            return False
            
    except Exception as e:
        print(f"❌ Bot setup test failed: {e}")
        return False

async def main():
    """Run all tests"""
    print("🕉️ Bhagavad Gita Bot Test Suite")
    print("=" * 40)
    
    tests = [
        ("Imports", test_imports),
        ("Configuration", test_config),
        ("Database", test_database),
        ("OpenRouter API", test_openrouter),
        ("Bot Setup", test_bot_setup),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name} test...")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                passed += 1
                print(f"✅ {test_name} test PASSED")
            else:
                print(f"❌ {test_name} test FAILED")
        except Exception as e:
            print(f"❌ {test_name} test ERROR: {e}")
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Your bot is ready to run.")
        print("\n🚀 To start the bot, run: python main.py")
    else:
        print("⚠️  Some tests failed. Please check the configuration and dependencies.")
        print("\n📖 See SETUP_GUIDE.md for detailed setup instructions.")

if __name__ == "__main__":
    asyncio.run(main())
