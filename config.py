import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Config:
    """Configuration class for the Gita Bot"""
    
    # Telegram Bot Configuration
    TELEGRAM_BOT_TOKEN = os.getenv('TELEGRAM_BOT_TOKEN')
    
    # OpenRouter API Configuration
    OPENROUTER_API_KEY = os.getenv('OPENROUTER_API_KEY')
    OPENROUTER_MODEL = os.getenv('OPENROUTER_MODEL', 'deepseek/deepseek-chat-v3.1:free')
    OPENROUTER_BASE_URL = "https://openrouter.ai/api/v1"
    
    # Database Configuration
    DATABASE_PATH = os.getenv('DATABASE_PATH', 'gita_bot.db')
    
    # Scheduling Configuration
    DAILY_SEND_TIME = os.getenv('DAILY_SEND_TIME', '04:00')
    TIMEZONE = os.getenv('TIMEZONE', 'Asia/Kolkata')
    
    # Bot Limits Configuration
    MAX_GROUP_VERSES_PER_DAY = int(os.getenv('MAX_GROUP_VERSES_PER_DAY', 5))
    MAX_USER_VERSES_PER_DAY = int(os.getenv('MAX_USER_VERSES_PER_DAY', 1))
    
    # Logging Configuration
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FILE = os.getenv('LOG_FILE', 'gita_bot.log')
    
    @classmethod
    def validate_config(cls):
        """Validate that all required configuration is present"""
        required_vars = [
            'TELEGRAM_BOT_TOKEN',
            'OPENROUTER_API_KEY'
        ]
        
        missing_vars = []
        for var in required_vars:
            if not getattr(cls, var):
                missing_vars.append(var)
        
        if missing_vars:
            raise ValueError(f"Missing required environment variables: {', '.join(missing_vars)}")
        
        return True
