# 🚀 Bhagavad Gita Bot Setup Guide

This guide will walk you through setting up the Bhagavad Gita Telegram Bot step by step.

## 📋 Prerequisites

Before you begin, ensure you have:
- Python 3.8 or higher installed
- A Telegram account
- Basic familiarity with command line/terminal

## 🔑 Step 1: Get Required API Keys

### 1.1 Telegram Bot Token

1. Open Telegram and search for `@BotFather`
2. Start a chat with Bo<PERSON><PERSON>ather and send `/newbot`
3. Follow the prompts to create your bot:
   - Choose a name for your bot (e.g., "My Gita Bot")
   - Choose a username ending in "bot" (e.g., "my_gita_wisdom_bot")
4. BotFather will provide you with a token like: `*********:ABCdefGHIjklMNOpqrsTUVwxyz`
5. Save this token securely - you'll need it later

### 1.2 OpenRouter API Key

1. Visit [OpenRouter.ai](https://openrouter.ai/)
2. Sign up for an account
3. Go to your dashboard and create an API key
4. Copy the API key (starts with `sk-or-v1-...`)
5. Save this key securely

## 💻 Step 2: Setup the Project

### 2.1 Download and Install

1. **Download the project files** to your computer
2. **Open terminal/command prompt** and navigate to the project directory:
   ```bash
   cd path/to/gita-bot
   ```

3. **Install Python dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

### 2.2 Configure Environment Variables

1. **Copy the example environment file:**
   ```bash
   cp .env.example .env
   ```

2. **Edit the .env file** with your favorite text editor and add your tokens:
   ```env
   # Required - Add your actual tokens here
   TELEGRAM_BOT_TOKEN=*********:ABCdefGHIjklMNOpqrsTUVwxyz
   OPENROUTER_API_KEY=sk-or-v1-your-actual-api-key-here
   
   # Optional - You can keep these defaults
   OPENROUTER_MODEL=deepseek/deepseek-chat-v3.1:free
   DATABASE_PATH=gita_bot.db
   DAILY_SEND_TIME=04:00
   TIMEZONE=Asia/Kolkata
   MAX_GROUP_VERSES_PER_DAY=5
   MAX_USER_VERSES_PER_DAY=1
   LOG_LEVEL=INFO
   LOG_FILE=gita_bot.log
   ```

## 🧪 Step 3: Test the Setup

1. **Test the bot configuration:**
   ```bash
   python main.py --test
   ```

2. **If successful, you should see:**
   ```
   Configuration validated successfully
   Database test: Found 0 subscribed groups
   OpenRouter API test: Successfully generated verse content
   Functionality test completed
   ```

3. **If you see errors:**
   - Check that your API keys are correct
   - Ensure you have internet connectivity
   - Verify all dependencies are installed

## 🚀 Step 4: Run the Bot

1. **Start the bot:**
   ```bash
   python main.py
   ```

2. **You should see:**
   ```
   Bot is now running. Press Ctrl+C to stop.
   ```

3. **The bot is now active!** Keep this terminal window open.

## 📱 Step 5: Test with Telegram

### 5.1 Test Private Chat

1. **Find your bot** on Telegram by searching for its username
2. **Start a chat** and send `/start`
3. **Try the `/verse` command** to get a verse
4. **Check `/status`** to see your usage

### 5.2 Test Group Features

1. **Create a test group** or use an existing one
2. **Add your bot** to the group
3. **Send `/start`** in the group
4. **Try `/subscribe`** to subscribe to daily verses
5. **Use `/verse`** to test manual verse generation
6. **Check `/status`** to see group usage

## ⚙️ Step 6: Configure Daily Scheduling

The bot automatically sends verses at 4:00 AM IST to subscribed groups. To change this:

1. **Edit the .env file:**
   ```env
   DAILY_SEND_TIME=06:00  # Change to 6:00 AM
   TIMEZONE=America/New_York  # Change timezone if needed
   ```

2. **Restart the bot** for changes to take effect

## 📊 Step 7: Monitor the Bot

### 7.1 Check Logs

- **Main log:** `gita_bot.log`
- **Error log:** `gita_bot_errors.log`
- **Log directory:** `logs/` (if configured)

### 7.2 Database

The bot creates a SQLite database file (`gita_bot.db`) to track:
- User information
- Group subscriptions
- Daily usage limits
- Verse history

## 🔧 Troubleshooting

### Common Issues

**Bot doesn't respond:**
- Verify bot token is correct
- Check if bot is added to the group
- Look at logs for error messages

**"Configuration validation failed":**
- Check that both API keys are set in .env
- Ensure no extra spaces in the .env file

**Verses not generating:**
- Verify OpenRouter API key
- Check internet connection
- Review OpenRouter account limits

**Scheduled verses not working:**
- Ensure bot is running continuously
- Check timezone settings
- Verify groups are subscribed

### Getting Help

1. **Check the logs** first for error messages
2. **Verify your configuration** in the .env file
3. **Test with the `--test` flag** to isolate issues

## 🎯 Next Steps

Once your bot is running:

1. **Invite friends** to test the bot
2. **Subscribe groups** to daily verses
3. **Monitor usage** through logs and status commands
4. **Customize settings** in the .env file as needed

## 🔒 Security Notes

- **Keep your API keys secret** - never share them publicly
- **Use environment variables** - don't hardcode keys in the code
- **Backup your database** regularly if you have important data
- **Monitor logs** for any suspicious activity

---

**Congratulations! Your Bhagavad Gita Bot is now ready to spread wisdom! 🕉️**
