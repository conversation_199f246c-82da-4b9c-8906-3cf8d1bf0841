import sqlite3
import logging
from datetime import datetime, date
from typing import Optional, List, Tuple
from config import Config

class DatabaseManager:
    """Database manager for the Gita Bot"""
    
    def __init__(self, db_path: str = None):
        self.db_path = db_path or Config.DATABASE_PATH
        self.logger = logging.getLogger(__name__)
        self.init_database()
    
    def get_connection(self) -> sqlite3.Connection:
        """Get database connection with row factory"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def init_database(self):
        """Initialize database with required tables"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # Users table for tracking individual users
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    user_id INTEGER PRIMARY KEY,
                    username TEXT,
                    first_name TEXT,
                    last_name TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    is_active BOOLEAN DEFAULT 1
                )
            ''')
            
            # Groups table for tracking subscribed groups
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS groups (
                    group_id INTEGER PRIMARY KEY,
                    group_title TEXT,
                    group_type TEXT,
                    is_subscribed BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Daily usage tracking for users
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_daily_usage (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    usage_date DATE,
                    verse_count INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (user_id),
                    UNIQUE(user_id, usage_date)
                )
            ''')
            
            # Daily usage tracking for groups
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS group_daily_usage (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    group_id INTEGER,
                    usage_date DATE,
                    verse_count INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (group_id) REFERENCES groups (group_id),
                    UNIQUE(group_id, usage_date)
                )
            ''')
            
            # Verse history for tracking sent verses
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS verse_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    chat_id INTEGER,
                    chat_type TEXT,
                    verse_content TEXT,
                    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    is_scheduled BOOLEAN DEFAULT 0
                )
            ''')
            
            conn.commit()
            self.logger.info("Database initialized successfully")
    
    def add_or_update_user(self, user_id: int, username: str = None, 
                          first_name: str = None, last_name: str = None) -> bool:
        """Add or update user information"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR REPLACE INTO users 
                    (user_id, username, first_name, last_name)
                    VALUES (?, ?, ?, ?)
                ''', (user_id, username, first_name, last_name))
                conn.commit()
                return True
        except Exception as e:
            self.logger.error(f"Error adding/updating user {user_id}: {e}")
            return False
    
    def add_or_update_group(self, group_id: int, group_title: str = None,
                           group_type: str = None, is_subscribed: bool = True) -> bool:
        """Add or update group information"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR REPLACE INTO groups
                    (group_id, group_title, group_type, is_subscribed, updated_at)
                    VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)
                ''', (group_id, group_title, group_type, is_subscribed))
                conn.commit()
                return True
        except Exception as e:
            self.logger.error(f"Error adding/updating group {group_id}: {e}")
            return False

    def get_user_daily_usage(self, user_id: int, usage_date: date = None) -> int:
        """Get user's daily verse usage count"""
        if usage_date is None:
            usage_date = date.today()

        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT verse_count FROM user_daily_usage
                    WHERE user_id = ? AND usage_date = ?
                ''', (user_id, usage_date))
                result = cursor.fetchone()
                return result['verse_count'] if result else 0
        except Exception as e:
            self.logger.error(f"Error getting user daily usage for {user_id}: {e}")
            return 0

    def get_group_daily_usage(self, group_id: int, usage_date: date = None) -> int:
        """Get group's daily verse usage count"""
        if usage_date is None:
            usage_date = date.today()

        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT verse_count FROM group_daily_usage
                    WHERE group_id = ? AND usage_date = ?
                ''', (group_id, usage_date))
                result = cursor.fetchone()
                return result['verse_count'] if result else 0
        except Exception as e:
            self.logger.error(f"Error getting group daily usage for {group_id}: {e}")
            return 0

    def increment_user_usage(self, user_id: int, usage_date: date = None) -> bool:
        """Increment user's daily verse usage count"""
        if usage_date is None:
            usage_date = date.today()

        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR IGNORE INTO user_daily_usage (user_id, usage_date, verse_count)
                    VALUES (?, ?, 0)
                ''', (user_id, usage_date))

                cursor.execute('''
                    UPDATE user_daily_usage
                    SET verse_count = verse_count + 1
                    WHERE user_id = ? AND usage_date = ?
                ''', (user_id, usage_date))
                conn.commit()
                return True
        except Exception as e:
            self.logger.error(f"Error incrementing user usage for {user_id}: {e}")
            return False

    def increment_group_usage(self, group_id: int, usage_date: date = None) -> bool:
        """Increment group's daily verse usage count"""
        if usage_date is None:
            usage_date = date.today()

        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR IGNORE INTO group_daily_usage (group_id, usage_date, verse_count)
                    VALUES (?, ?, 0)
                ''', (group_id, usage_date))

                cursor.execute('''
                    UPDATE group_daily_usage
                    SET verse_count = verse_count + 1
                    WHERE group_id = ? AND usage_date = ?
                ''', (group_id, usage_date))
                conn.commit()
                return True
        except Exception as e:
            self.logger.error(f"Error incrementing group usage for {group_id}: {e}")
            return False

    def get_subscribed_groups(self) -> List[int]:
        """Get list of subscribed group IDs"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT group_id FROM groups WHERE is_subscribed = 1
                ''')
                return [row['group_id'] for row in cursor.fetchall()]
        except Exception as e:
            self.logger.error(f"Error getting subscribed groups: {e}")
            return []

    def subscribe_group(self, group_id: int) -> bool:
        """Subscribe a group to daily verses"""
        return self.add_or_update_group(group_id, is_subscribed=True)

    def unsubscribe_group(self, group_id: int) -> bool:
        """Unsubscribe a group from daily verses"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE groups SET is_subscribed = 0, updated_at = CURRENT_TIMESTAMP
                    WHERE group_id = ?
                ''', (group_id,))
                conn.commit()
                return True
        except Exception as e:
            self.logger.error(f"Error unsubscribing group {group_id}: {e}")
            return False

    def add_verse_history(self, chat_id: int, chat_type: str, verse_content: str,
                         is_scheduled: bool = False) -> bool:
        """Add verse to history"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO verse_history (chat_id, chat_type, verse_content, is_scheduled)
                    VALUES (?, ?, ?, ?)
                ''', (chat_id, chat_type, verse_content, is_scheduled))
                conn.commit()
                return True
        except Exception as e:
            self.logger.error(f"Error adding verse history: {e}")
            return False
