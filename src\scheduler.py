import asyncio
import schedule
import threading
import logging
from datetime import datetime, time
import pytz
from config import Config
from src.database import DatabaseManager

class VerseScheduler:
    """Scheduler for sending daily verses to subscribed groups"""
    
    def __init__(self, bot_instance):
        self.bot = bot_instance
        self.db = DatabaseManager()
        self.logger = logging.getLogger(__name__)
        self.timezone = pytz.timezone(Config.TIMEZONE)
        self.is_running = False
        self.scheduler_thread = None
        
    def setup_schedule(self):
        """Setup the daily schedule for verse sending"""
        # Schedule daily verse sending at 4:00 AM IST
        schedule.every().day.at(Config.DAILY_SEND_TIME).do(self._run_daily_verse_job)
        self.logger.info(f"Scheduled daily verses at {Config.DAILY_SEND_TIME} {Config.TIMEZONE}")
    
    def _run_daily_verse_job(self):
        """Run the daily verse sending job"""
        try:
            self.logger.info("Starting daily verse sending job")
            
            # Get current time in IST
            current_time = datetime.now(self.timezone)
            self.logger.info(f"Daily job triggered at {current_time}")
            
            # Run the async job
            asyncio.run(self._send_daily_verses())
            
        except Exception as e:
            self.logger.error(f"Error in daily verse job: {e}")
    
    async def _send_daily_verses(self):
        """Send daily verses to all subscribed groups"""
        try:
            # Get all subscribed groups
            subscribed_groups = self.db.get_subscribed_groups()
            
            if not subscribed_groups:
                self.logger.info("No subscribed groups found")
                return
            
            self.logger.info(f"Sending daily verses to {len(subscribed_groups)} groups")
            
            # Send verses to each subscribed group
            for group_id in subscribed_groups:
                try:
                    await self.bot.send_scheduled_verse_to_group(group_id)
                    # Add a small delay between sends to avoid rate limiting
                    await asyncio.sleep(1)
                    
                except Exception as e:
                    self.logger.error(f"Failed to send daily verse to group {group_id}: {e}")
                    continue
            
            self.logger.info("Daily verse sending completed")
            
        except Exception as e:
            self.logger.error(f"Error in daily verse sending: {e}")
    
    def start_scheduler(self):
        """Start the scheduler in a separate thread"""
        if self.is_running:
            self.logger.warning("Scheduler is already running")
            return
        
        self.setup_schedule()
        self.is_running = True
        
        def run_scheduler():
            """Run the scheduler loop"""
            self.logger.info("Scheduler thread started")
            while self.is_running:
                try:
                    schedule.run_pending()
                    # Check every minute
                    threading.Event().wait(60)
                except Exception as e:
                    self.logger.error(f"Error in scheduler loop: {e}")
                    threading.Event().wait(60)
            
            self.logger.info("Scheduler thread stopped")
        
        self.scheduler_thread = threading.Thread(target=run_scheduler, daemon=True)
        self.scheduler_thread.start()
        self.logger.info("Verse scheduler started")
    
    def stop_scheduler(self):
        """Stop the scheduler"""
        if not self.is_running:
            self.logger.warning("Scheduler is not running")
            return
        
        self.is_running = False
        schedule.clear()
        
        if self.scheduler_thread and self.scheduler_thread.is_alive():
            self.scheduler_thread.join(timeout=5)
        
        self.logger.info("Verse scheduler stopped")
    
    def get_next_scheduled_time(self) -> datetime:
        """Get the next scheduled verse time"""
        try:
            # Get the next scheduled job
            jobs = schedule.get_jobs()
            if jobs:
                next_run = jobs[0].next_run
                if next_run:
                    # Convert to IST
                    return next_run.replace(tzinfo=self.timezone)
            
            # If no jobs or next_run is None, calculate next 4:00 AM IST
            now = datetime.now(self.timezone)
            next_time = now.replace(hour=4, minute=0, second=0, microsecond=0)
            
            # If 4:00 AM has already passed today, schedule for tomorrow
            if next_time <= now:
                next_time = next_time.replace(day=next_time.day + 1)
            
            return next_time
            
        except Exception as e:
            self.logger.error(f"Error getting next scheduled time: {e}")
            return None
    
    def get_scheduler_status(self) -> dict:
        """Get current scheduler status"""
        next_time = self.get_next_scheduled_time()
        
        return {
            'is_running': self.is_running,
            'next_scheduled_time': next_time,
            'timezone': Config.TIMEZONE,
            'daily_send_time': Config.DAILY_SEND_TIME,
            'subscribed_groups_count': len(self.db.get_subscribed_groups())
        }
    
    async def test_daily_verse_send(self, test_group_id: int = None):
        """Test the daily verse sending functionality"""
        try:
            self.logger.info("Testing daily verse sending...")
            
            if test_group_id:
                # Test with specific group
                await self.bot.send_scheduled_verse_to_group(test_group_id)
                self.logger.info(f"Test verse sent to group {test_group_id}")
            else:
                # Test with all subscribed groups
                await self._send_daily_verses()
                self.logger.info("Test daily verses sent to all subscribed groups")
                
        except Exception as e:
            self.logger.error(f"Error in test daily verse send: {e}")
            raise
